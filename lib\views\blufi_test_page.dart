import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../services/blufi_service_copy.dart';

class BlufiTestPage extends StatefulWidget {
  const BlufiTestPage({super.key});

  @override
  State<BlufiTestPage> createState() => _BlufiTestPageState();
}

class _BlufiTestPageState extends State<BlufiTestPage> {
  late BlufiService _blufiService;
  String _connectionStatus = '未连接';
  bool _isConfigured = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _blufiService = Provider.of<BlufiService>(context, listen: false);
      // 设置连接状态监听回调
      _blufiService.onConnectionStateChanged = (state) {
        setState(() {
          switch (state) {
            case BluetoothConnectionState.disconnected:
              _connectionStatus = '已断开';
              if (_isConfigured) {
                setState(() {
                  _connectionStatus = '配网完成....';
                });
              }
              break;
            case BluetoothConnectionState.connected:
              _connectionStatus = '已连接';
              break;
            default:
              _connectionStatus = '状态变化中...';
              break;
          }
        });
      };
    });
  }

  @override
  void dispose() {
    _blufiService.dispose();
    super.dispose();
  }

  //测试
  Future<void> _test() async {
    var deviceName = 'PETCARE';
    setState(() {
      _connectionStatus = '扫描设备中...';
    });
    final hasDevice = await _blufiService.scanForDevice(deviceName);
    if (!hasDevice) {
      setState(() {
        _connectionStatus = '未找到设备';
      });
      return;
    }
    setState(() {
      _connectionStatus = '连接设备中...';
    });
    final connected = await _blufiService.connectDeviceWithListener();
    if (connected) {
      setState(() {
        _connectionStatus = '连接成功';
      });
      // 发送测试数据
      String device_id = await _blufiService.sendCustomData('get_devicename');
      // 连接成功后，BluFi数据监听服务已经自动设置
      _isConfigured = await _blufiService.getWiFiStatus();
      await Future.delayed(const Duration(seconds: 1));
      _isConfigured &= await _blufiService.sendSSID('K50');
      await Future.delayed(const Duration(seconds: 1));
      _isConfigured &= await _blufiService.sendPassword('12345688');
      await Future.delayed(const Duration(seconds: 1));
      _isConfigured &= await _blufiService.sendConnectCommand();
    } else {
      setState(() {
        _connectionStatus = '连接失败';
      });
    }

    if (_isConfigured) {
      setState(() {
        _connectionStatus = '配置成功.....';
      });
    }
  }

  // Future<void> _test() async {
  //   const deviceName = 'PETCARE';
  //   BluetoothDevice? targetDevice;

  //   setState(() {
  //     _connectionStatus = '扫描设备中...';
  //   });

  //   var subscription = FlutterBluePlus.scanResults.listen((results) {
  //     for (ScanResult r in results) {
  //       targetDevice = r.device;
  //       // 找到设备后立即停止扫描
  //       FlutterBluePlus.stopScan();
  //     }
  //   });

  //   // 开始扫描
  //   await FlutterBluePlus.startScan(
  //       withNames: [deviceName], timeout: const Duration(seconds: 5));

  //   // 等待一小段时间确保扫描结果被处理
  //   await Future.delayed(const Duration(seconds: 5));
  //   await subscription.cancel();

  //   if (targetDevice == null) {
  //     setState(() {
  //       _connectionStatus = '未找到设备';
  //     });
  //     return;
  //   }

  //   setState(() {
  //     _connectionStatus = '连接设备中...';
  //   });

  //   // 使用新的连接方法，带状态监听
  //   bool connected = await _blufiService.connectDeviceWithListener(
  //       targetDevice!,
  //       timeout: const Duration(seconds: 10));

  //   if (connected) {
  //     // 连接成功后，BluFi数据监听服务已经自动设置
  //     final getstatus = _blufiService.createWifiStatusFrame();
  //     await _blufiService.writeBlufiData(targetDevice!, getstatus);
  //     await Future.delayed(const Duration(seconds: 1));
  //     // 发送测试数据
  //     List<int> sendData = []; // 自定义数据帧内容，可以根据需要修改
  //     // 字符串转换为字节数组
  //     String dataString = 'n10';
  //     String hexString = dataString.codeUnits
  //         .map((byte) => byte.toRadixString(16).padLeft(2, '0'))
  //         .join('');
  //     for (int i = 0; i < hexString.length; i += 2) {
  //       sendData.add(int.parse(hexString.substring(i, i + 2), radix: 16));
  //     }
  //     final pdata = _blufiService.createStaSsidDataFrame(sendData); // 自定义数据帧
  //     await _blufiService.writeBlufiData(targetDevice!, pdata);
  //     await Future.delayed(const Duration(seconds: 1));
  //     List<int> sendData2 = [];
  //     String dataString2 = '12345678';
  //     String hexString2 = dataString2.codeUnits
  //         .map((byte) => byte.toRadixString(16).padLeft(2, '0'))
  //         .join('');
  //     for (int i = 0; i < hexString2.length; i += 2) {
  //       sendData2.add(int.parse(hexString2.substring(i, i + 2), radix: 16));
  //     }
  //     final pdata2 =
  //         _blufiService.createStaPasswordDataFrame(sendData2); // 自定义数据帧
  //     await _blufiService.writeBlufiData(targetDevice!, pdata2);
  //     await Future.delayed(const Duration(seconds: 1));
  //     final pdata3 = _blufiService.createConnectAPFrame(); // 自定义数据帧
  //     await _blufiService.writeBlufiData(targetDevice!, pdata3);
  //   } else {
  //     setState(() {
  //       _connectionStatus = '连接失败';
  //     });
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('BluFi测试'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 连接状态显示
            Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  const Text(
                    '连接状态',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _connectionStatus,
                    style: TextStyle(
                      fontSize: 16,
                      color: _connectionStatus == '已连接'
                          ? Colors.green
                          : _connectionStatus == '已断开' ||
                                  _connectionStatus == '连接失败' ||
                                  _connectionStatus == '未找到设备'
                              ? Colors.red
                              : Colors.orange,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            // 测试连接按钮
            ElevatedButton(
              onPressed: _test,
              child: const Text('测试连接'),
            ),
            const SizedBox(height: 10),
            // 断开连接按钮
            if (_connectionStatus == '已连接')
              ElevatedButton(
                onPressed: _blufiService.isDeviceConnected()
                    ? () async {
                        await _blufiService.disconnectDevice();
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('断开连接'),
              ),
          ],
        ),
      ),
    );
  }
}
