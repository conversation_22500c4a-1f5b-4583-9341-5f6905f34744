import 'dart:async';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:logger/logger.dart'; // 核心日志库

class BlufiService {
  final Logger _logger = Logger();
  // 添加一个Completer来处理ACK
  Completer<dynamic>? _commandCompleter;
  StreamSubscription<List<int>>? _dataSubscription;

  // 蓝牙连接状态监听
  StreamSubscription<BluetoothConnectionState>? _connectionStateSubscription;
  BluetoothDevice? _currentDevice;
  BluetoothConnectionState _currentConnectionState =
      BluetoothConnectionState.disconnected;

  // 连接状态回调
  Function(BluetoothConnectionState)? onConnectionStateChanged;

  final String blufiServiceUuid = "0000ffff-0000-1000-8000-00805f9b34fb";
  final String writeCharUuid = "0000ff01-0000-1000-8000-00805f9b34fb";
  final String notifyCharUuid = "0000ff02-0000-1000-8000-00805f9b34fb";
  // 帧类型定义
  final int typeCtrl = 0x0;
  final int typeData = 0x1;

  // 子类型定义 ?应该做一个枚举类型
  final int subtypeVersion = 0x7;
  final int subtypeWifiStatus = 0x5;
  final int subtypeCustomData = 0x13;
  final subtypeStaPassward = 0x3;
  final subtypeStaSsid = 0x2;
  final subtypeConnectAP = 0x3;

  // 创建获取版本信息的帧
  List<int> createVersionFrame() {
    return _createFrame(typeData, subtypeVersion, []);
  }

  // 发送ssid
  List<int> createStaSsidDataFrame(List<int> data) {
    return _createFrame(typeData, subtypeStaSsid, data);
  }

  List<int> createStaPasswordDataFrame(List<int> data) {
    return _createFrame(typeData, subtypeStaPassward, data);
  }

  List<int> createConnectAPFrame() {
    return _createFrame(typeCtrl, subtypeConnectAP, []);
  }

  // 创建获取WiFi状态的帧
  List<int> createWifiStatusFrame() {
    return _createFrame(typeCtrl, subtypeWifiStatus, []);
  }

  // 创建自定义数据帧
  List<int> createCustomDataFrame(List<int> data) {
    return _createFrame(typeData, subtypeCustomData, data, isRequireAck: false);
  }

  // 创建帧
  List<int> _createFrame(int type, int subType, List<int> data,
      {bool isRequireAck = true}) {
    List<int> frame = [];
    frame.add((type & 0x1) | ((subType & 0x3f) << 2)); // 类型和子类型
    frame.add(isRequireAck ? 0x8 : 0x0); // 帧控制
    frame.add(_getSequence()); // 序列号
    frame.add(data.length); // 数据长度
    frame.addAll(data); // 数据
    // frame.addAll(_calculateChecksum(frame)); // 添加效验数据
    return frame;
  }

  // 获取序列号
  int _sequence = 0;
  void resetSequence(bool isReset) {
    if (isReset) {
      _sequence = 0;
    } else {
      _sequence = (_sequence - 1) & 0xff;
    }
  }

  int _getSequence() {
    int currentSequence = _sequence & 0xff;
    _sequence = (_sequence + 1) & 0xff;
    return currentSequence;
  }

  // 计算校验，BLufi的效验比较复杂，暂时不实现

  // 解析响应
  Map<String, dynamic> parseResponse(List<int> data) {
    if (data.length < 4) return {'error': '数据长度不足'};

    final type = data[0] & 0x1;
    final subType = (data[0] >> 2) & 0x3f;
    final frameCtrl = data[1];
    final sequence = data[2];
    final dataLen = data[3];

    Map<String, dynamic> result = {
      'type': type,
      'subType': subType,
      'frameCtrl': frameCtrl,
      'sequence': sequence,
      'dataLength': dataLen,
    };

    if (data.length > 4) {
      result['data'] = data.sublist(4, 4 + dataLen);
    }

    _logger.i(
        'BlufiService收到原始数据(HEX): ${data.map((b) => b.toRadixString(16).padLeft(2, '0')).join(',')}');

    // ASCII格式打印
    // String asciiText = String.fromCharCodes(data);
    // _logger.i('BlufiService收到ASCII数据: $asciiText');

    // 如果需要同时显示ASCII码值
    // String asciiValues = data.map((b) => 'ASCII($b)').join(',');
    // _logger.i('BlufiService收到ASCII码值: $asciiValues');

    return result;
  }

  // 设置数据监听
  Future<void> setupBlufiDataListener(BluetoothDevice device) async {
    final services = await device.discoverServices();
    for (var service in services) {
      if (service.uuid == Guid(blufiServiceUuid)) {
        for (var char in service.characteristics) {
          if (char.uuid == Guid(notifyCharUuid)) {
            await char.setNotifyValue(true);
            _dataSubscription = char.onValueReceived.listen(
              (value) {
                if (value.isNotEmpty) {
                  _logger.i('接收到的蓝牙数据: $value');
                  final response = parseResponse(value);

                  if ((response['subType'] << 2 | response['type']) == 0x4d) {
                    // 处理返回自定义信息
                    String customData = String.fromCharCodes(response['data']);
                    _logger.i('收到数据: $customData');
                    _commandCompleter?.complete(customData);
                  } else if ((response['subType'] << 2 | response['type']) ==
                      0x49) {
                    //返回erro
                    _commandCompleter?.complete(false);

                    resetSequence(false);
                  } else if ((response['subType'] << 2 | response['type']) ==
                      0x00) {
                    //返回OK
                    _commandCompleter?.complete(true);
                  }
                }
              },
              onError: (error) {
                _logger.e('数据监听错误: $error', error: error);
                _commandCompleter?.complete(false);
              },
            );
            return;
          }
        }
      }
    }
  }

  // 在不需要时取消对Blufi的监听
  void disposeBlufiDataListener() {
    _dataSubscription?.cancel();
    _dataSubscription = null;
  }

  // 通过蓝牙发送数据
  Future<void> writeBlufiData(BluetoothDevice device, List<int> data) async {
    try {
      final services = await device.discoverServices();
      for (var service in services) {
        if (service.uuid == Guid(blufiServiceUuid)) {
          for (var char in service.characteristics) {
            if (char.uuid == Guid(writeCharUuid)) {
              await char.write(data);
              return;
            }
          }
        }
      }
    } catch (e, stackTrace) {
      _logger.w('发送蓝牙数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  // 开始监听蓝牙连接状态
  void startConnectionStateListener() {
    _logger.i('开始监听设备连接状态: ${_currentDevice!.platformName}');

    // 取消之前的监听
    _connectionStateSubscription?.cancel();

    // 监听连接状态变化
    _connectionStateSubscription = _currentDevice!.connectionState.listen(
      (BluetoothConnectionState state) async {
        _logger.i('蓝牙连接状态变化: $state');

        // 更新当前连接状态
        _currentConnectionState = state;

        // 调用外部回调
        onConnectionStateChanged?.call(state);

        // 当连接成功时，自动设置读写通知服务
        if (state == BluetoothConnectionState.connected) {
          _logger.i('蓝牙连接成功，开始设置BluFi数据监听服务');
          try {
            await setupBlufiDataListener(_currentDevice!);
            _logger.i('BluFi数据监听服务设置成功');
          } catch (e) {
            _logger.e('设置BluFi数据监听服务失败: $e');
          }
        } else if (state == BluetoothConnectionState.disconnected) {
          _logger.i('蓝牙连接断开，清理数据监听');
          disposeBlufiDataListener();
        }
      },
      onError: (error) {
        _logger.e('连接状态监听错误: $error');
      },
    );
  }

  // 停止监听蓝牙连接状态
  void stopConnectionStateListener() {
    _logger.i('停止监听蓝牙连接状态');
    _connectionStateSubscription?.cancel();
    _connectionStateSubscription = null;
    _currentDevice = null;
    _currentConnectionState = BluetoothConnectionState.disconnected;
    resetSequence(true);
  }

  // 获取当前设备的连接状态
  BluetoothConnectionState getCurrentConnectionState() {
    return _currentConnectionState;
  }

  // 检查设备是否已连接
  bool isDeviceConnected() {
    return _currentConnectionState == BluetoothConnectionState.connected;
  }

  // 连接设备（带状态监听）
  Future<bool> connectDeviceWithListener({Duration? timeout}) async {
    try {
      _logger.i('开始连接设备: ${_currentDevice!.platformName}');

      // 先开始监听连接状态
      startConnectionStateListener();

      // 连接设备
      await _currentDevice!
          .connect(timeout: timeout ?? const Duration(seconds: 15));

      // 等待连接状态更新
      await Future.delayed(const Duration(milliseconds: 500));

      if (isDeviceConnected()) {
        _logger.i('设备连接成功');
        return true;
      } else {
        _logger.w('设备连接失败');
        return false;
      }
    } catch (e) {
      _logger.e('连接设备时发生错误: $e');
      return false;
    }
  }

  // 断开设备连接
  Future<void> disconnectDevice() async {
    if (_currentDevice != null) {
      try {
        _logger.i('断开设备连接: ${_currentDevice!.platformName}');
        await _currentDevice!.disconnect();
      } catch (e) {
        _logger.e('断开设备连接时发生错误: $e');
      }
    }
  }

  //扫描设备,如果存在设备则返回ture
  Future<bool> scanForDevice(String deviceName,
      {Duration timeout = const Duration(seconds: 5)}) async {
    // 检查蓝牙是否开启
    if (await FlutterBluePlus.isSupported == false) {
      throw UnsupportedError('蓝牙未开启，无法扫描设备'); //抛出异常
    }
    var subscription = FlutterBluePlus.scanResults.listen((results) {
      for (ScanResult r in results) {
        _currentDevice = r.device; // 找到设备，保存到targetDevice变量中
        // 找到设备后立即停止扫描
        FlutterBluePlus.stopScan();
      }
    });

    // 开始扫描
    await FlutterBluePlus.startScan(withNames: [deviceName], timeout: timeout);

    // 等待一小段时间确保扫描结果被处理
    await Future.delayed(timeout); // 等待扫描完成
    await subscription.cancel();
    return _currentDevice != null;
  }

  //获取项圈WIFI状态
  Future<bool> getWiFiStatus() async {
    // 创建新的Completer等待响应
    _commandCompleter = Completer<dynamic>();

    final getstatus = createWifiStatusFrame();
    await writeBlufiData(_currentDevice!, getstatus);

    // 等待响应（带超时）
    try {
      dynamic result = await _commandCompleter!.future.timeout(
        const Duration(seconds: 2),
        onTimeout: () => false,
      );
      // 处理不同类型的响应
      if (result == true) {
        return true;
      } else {
        return false;
      }
    } finally {
      _commandCompleter = null;
    }
  }

  //发送自定义数据
  Future<String> sendCustomData(String dataString) async {
    // 创建新的Completer等待响应
    _commandCompleter = Completer<dynamic>();

    List<int> sendData = []; // 自定义数据帧内容，可以根据需要修改
    // 字符串转换为字节数组
    String hexString = dataString.codeUnits
        .map((byte) => byte.toRadixString(16).padLeft(2, '0'))
        .join('');
    for (int i = 0; i < hexString.length; i += 2) {
      sendData.add(int.parse(hexString.substring(i, i + 2), radix: 16));
    }
    final pdata = createCustomDataFrame(sendData); // 自定义数据帧
    await writeBlufiData(_currentDevice!, pdata);

    // 等待响应（带超时）
    try {
      dynamic result = await _commandCompleter!.future.timeout(
        const Duration(seconds: 2),
        onTimeout: () => 'timeout/unknown',
      );
      // 处理不同类型的响应
      if (result is String) {
        return result;
      } else {
        return 'timeout/unknown';
      }
    } finally {
      _commandCompleter = null;
    }
  }

  //发送ssid
  Future<bool> sendSSID(String dataString) async {
    // 创建新的Completer等待响应
    _commandCompleter = Completer<dynamic>();
    List<int> sendData = []; // 自定义数据帧内容，可以根据需要修改
    // 字符串转换为字节数组
    String hexString = dataString.codeUnits
        .map((byte) => byte.toRadixString(16).padLeft(2, '0'))
        .join('');
    for (int i = 0; i < hexString.length; i += 2) {
      sendData.add(int.parse(hexString.substring(i, i + 2), radix: 16));
    }
    final pdata = createStaSsidDataFrame(sendData); // 自定义数据帧
    await writeBlufiData(_currentDevice!, pdata);

    // 等待响应（带超时）
    try {
      dynamic result = await _commandCompleter!.future.timeout(
        const Duration(seconds: 2),
        onTimeout: () => false,
      );
      // 处理不同类型的响应
      if (result == true) {
        return true;
      } else {
        return false;
      }
    } finally {
      _commandCompleter = null;
    }
  }

  //发送密码
  Future<bool> sendPassword(String dataString) async {
    // 创建新的Completer等待响应
    _commandCompleter = Completer<dynamic>();
    List<int> sendData = [];
    String hexString = dataString.codeUnits
        .map((byte) => byte.toRadixString(16).padLeft(2, '0'))
        .join('');
    for (int i = 0; i < hexString.length; i += 2) {
      sendData.add(int.parse(hexString.substring(i, i + 2), radix: 16));
    }
    final pdata = createStaPasswordDataFrame(sendData); // 自定义数据帧
    await writeBlufiData(_currentDevice!, pdata);

    // 等待响应（带超时）
    try {
      dynamic result = await _commandCompleter!.future.timeout(
        const Duration(seconds: 2),
        onTimeout: () => false,
      );
      // 处理不同类型的响应
      if (result == true) {
        return true;
      } else {
        return false;
      }
    } finally {
      _commandCompleter = null;
    }
  }

  //发送wifi连接命令
  Future<bool> sendConnectCommand() async {
    // 创建新的Completer等待响应
    _commandCompleter = Completer<dynamic>();
    final pdata = createConnectAPFrame(); // 自定义数据帧
    await writeBlufiData(_currentDevice!, pdata);
    // 等待响应（带超时）
    try {
      dynamic result = await _commandCompleter!.future.timeout(
        const Duration(seconds: 2),
        onTimeout: () => false,
      );
      // 处理不同类型的响应
      if (result == true) {
        return true;
      } else {
        return false;
      }
    } finally {
      _commandCompleter = null;
    }
  }

  // 清理所有资源
  void dispose() {
    _logger.i('清理BlufiService资源');
    disposeBlufiDataListener();
    stopConnectionStateListener();
  }
}
