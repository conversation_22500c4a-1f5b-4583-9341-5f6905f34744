/// 应用程序常量定义
class AppConstants {
  // 应用信息
  static const String appName = 'ESP32 BluFi';
  static const String appVersion = '1.0.0';
  
  // 布局常量
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;
  
  // 边框圆角
  static const double borderRadius = 8.0;
  static const double smallBorderRadius = 4.0;
  static const double largeBorderRadius = 12.0;
  
  // 字体大小
  static const double smallFontSize = 12.0;
  static const double defaultFontSize = 14.0;
  static const double largeFontSize = 16.0;
  static const double titleFontSize = 18.0;
  static const double headingFontSize = 20.0;
  
  // 图标大小
  static const double smallIconSize = 16.0;
  static const double defaultIconSize = 24.0;
  static const double largeIconSize = 32.0;
  
  // 动画时长
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // 网络超时
  static const Duration networkTimeout = Duration(seconds: 30);
  static const Duration bluetoothTimeout = Duration(seconds: 10);
  
  // 列表项高度
  static const double listItemHeight = 56.0;
  static const double compactListItemHeight = 48.0;
  
  // 按钮高度
  static const double buttonHeight = 48.0;
  static const double smallButtonHeight = 36.0;
  
  // 输入框高度
  static const double textFieldHeight = 48.0;
  
  // 卡片阴影
  static const double cardElevation = 2.0;
  static const double modalElevation = 8.0;
  
  // 分割线
  static const double dividerThickness = 1.0;
  
  // 最大宽度（用于响应式布局）
  static const double maxContentWidth = 600.0;
}

/// 应用程序颜色常量
class AppColors {
  // 主色调 - 基于ESP32的蓝色主题
  static const int primaryColorValue = 0xFF0175C2;
  static const int primaryLightColorValue = 0xFF4FC3F7;
  static const int primaryDarkColorValue = 0xFF01579B;
  
  // 辅助色
  static const int accentColorValue = 0xFF03DAC6;
  static const int accentLightColorValue = 0xFF66FFF9;
  static const int accentDarkColorValue = 0xFF00A896;
  
  // 状态颜色
  static const int successColorValue = 0xFF4CAF50;
  static const int warningColorValue = 0xFFFF9800;
  static const int errorColorValue = 0xFFF44336;
  static const int infoColorValue = 0xFF2196F3;
  
  // 中性色
  static const int backgroundColorValue = 0xFFFAFAFA;
  static const int surfaceColorValue = 0xFFFFFFFF;
  static const int cardColorValue = 0xFFFFFFFF;
  
  // 文本颜色
  static const int textPrimaryColorValue = 0xFF212121;
  static const int textSecondaryColorValue = 0xFF757575;
  static const int textDisabledColorValue = 0xFFBDBDBD;
  static const int textOnPrimaryColorValue = 0xFFFFFFFF;
  
  // 分割线和边框
  static const int dividerColorValue = 0xFFE0E0E0;
  static const int borderColorValue = 0xFFE0E0E0;
  
  // 蓝牙状态颜色
  static const int bluetoothConnectedColorValue = 0xFF4CAF50;
  static const int bluetoothDisconnectedColorValue = 0xFFF44336;
  static const int bluetoothScanningColorValue = 0xFF2196F3;
  
  // WiFi状态颜色
  static const int wifiConnectedColorValue = 0xFF4CAF50;
  static const int wifiDisconnectedColorValue = 0xFFF44336;
  static const int wifiConnectingColorValue = 0xFFFF9800;
  
  // 信号强度颜色
  static const int signalStrongColorValue = 0xFF4CAF50;
  static const int signalMediumColorValue = 0xFFFF9800;
  static const int signalWeakColorValue = 0xFFF44336;
}
