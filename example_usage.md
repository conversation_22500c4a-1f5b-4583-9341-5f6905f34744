# BluFi 蓝牙连接状态监听使用示例

## 功能概述

新实现的蓝牙连接状态监听功能包括：

1. **自动监听连接状态变化** - 实时监控蓝牙设备的连接状态
2. **自动设置读写通知服务** - 连接成功时自动启用BluFi数据监听
3. **状态回调机制** - 支持外部回调函数处理状态变化
4. **完整的生命周期管理** - 包括连接、断开、清理等操作

## 主要新增方法

### 1. 连接状态监听
```dart
// 开始监听蓝牙连接状态
void startConnectionStateListener(BluetoothDevice device)

// 停止监听蓝牙连接状态  
void stopConnectionStateListener()

// 设置连接状态变化回调
blufiService.onConnectionStateChanged = (BluetoothConnectionState state) {
  // 处理状态变化
};
```

### 2. 连接管理
```dart
// 连接设备（带状态监听）
Future<bool> connectDeviceWithListener(BluetoothDevice device, {Duration? timeout})

// 断开设备连接
Future<void> disconnectDevice()

// 检查设备是否已连接
bool isDeviceConnected()

// 获取当前连接状态
BluetoothConnectionState getCurrentConnectionState()
```

### 3. 资源清理
```dart
// 清理所有资源
void dispose()
```

## 使用示例

### 基本使用流程

```dart
class MyBlufiPage extends StatefulWidget {
  @override
  _MyBlufiPageState createState() => _MyBlufiPageState();
}

class _MyBlufiPageState extends State<MyBlufiPage> {
  late BlufiService _blufiService;
  String _connectionStatus = '未连接';

  @override
  void initState() {
    super.initState();
    _blufiService = BlufiService();
    
    // 设置连接状态监听回调
    _blufiService.onConnectionStateChanged = (state) {
      setState(() {
        switch (state) {
          case BluetoothConnectionState.disconnected:
            _connectionStatus = '已断开';
            break;
          case BluetoothConnectionState.connected:
            _connectionStatus = '已连接';
            break;
          default:
            _connectionStatus = '状态变化中...';
            break;
        }
      });
    };
  }

  Future<void> _connectDevice() async {
    // 扫描并连接设备
    BluetoothDevice? device = await _scanForDevice();
    if (device != null) {
      bool success = await _blufiService.connectDeviceWithListener(device);
      if (success) {
        print('连接成功，BluFi服务已自动启用');
      }
    }
  }

  @override
  void dispose() {
    _blufiService.dispose(); // 清理所有资源
    super.dispose();
  }
}
```

## 核心特性

### 1. 自动服务启用
当蓝牙连接成功时，系统会自动：
- 发现BluFi服务
- 启用通知特征值
- 设置数据监听器
- 开始接收BluFi数据

### 2. 状态同步
- 实时更新连接状态
- 支持UI状态同步
- 提供状态查询接口

### 3. 错误处理
- 连接失败自动重试
- 异常情况日志记录
- 资源自动清理

### 4. 生命周期管理
- 自动管理监听器生命周期
- 防止内存泄漏
- 支持多次连接/断开

## 注意事项

1. **资源清理**: 使用完毕后务必调用 `dispose()` 方法清理资源
2. **状态回调**: 建议在 `initState()` 中设置状态回调函数
3. **异常处理**: 连接操作建议使用 try-catch 包装
4. **UI更新**: 状态变化时记得调用 `setState()` 更新UI

## 完整工作流程

1. **初始化服务** - 创建BlufiService实例
2. **设置回调** - 配置连接状态变化回调
3. **扫描设备** - 查找目标BluFi设备
4. **连接设备** - 使用 `connectDeviceWithListener()` 连接
5. **自动启用** - 系统自动启用BluFi数据监听服务
6. **数据通信** - 进行BluFi协议通信
7. **断开连接** - 使用 `disconnectDevice()` 断开
8. **清理资源** - 调用 `dispose()` 清理

这个实现大大简化了BluFi设备的连接和管理流程，提供了更好的用户体验和更可靠的连接管理。
